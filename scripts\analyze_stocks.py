#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票分析便捷CLI工具
解决PowerShell heredoc兼容性问题，提供简单的命令行接口

用法：
python scripts/analyze_stocks.py "601869,600522,600487"
python scripts/analyze_stocks.py "601869,600522,600487" --mode analyst
python scripts/analyze_stocks.py "TSLA,AAPL" --benchmark "^GSPC"
"""

import sys
import json
import argparse
from stock_data_tool import StockDataTool

# 设置输出编码
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

def main():
    parser = argparse.ArgumentParser(description='股票数据分析工具')
    parser.add_argument('codes', help='股票代码，逗号分隔，如: 601869,600522,600487')
    parser.add_argument('--mode', choices=['quant', 'analyst'], default='analyst', 
                       help='输出模式: quant(量化) 或 analyst(分析师)')
    parser.add_argument('--benchmark', default='000300.SH', 
                       help='基准指数，默认沪深300')
    parser.add_argument('--period', default='9mo', 
                       help='数据周期，默认9个月')
    parser.add_argument('--format', choices=['json', 'summary'], default='json',
                       help='输出格式: json(完整数据) 或 summary(简要总结)')
    
    args = parser.parse_args()
    
    try:
        # 初始化工具
        tool = StockDataTool(
            benchmark=args.benchmark,
            period=args.period
        )
        
        # 解析股票代码
        codes = tool.parse_inputs(codes_text=args.codes)
        if not codes:
            print("错误: 未能解析出有效的股票代码", file=sys.stderr)
            return 1
        
        print(f"解析到股票代码: {codes}", file=sys.stderr)
        
        # 获取数据
        result = tool.fetch(codes)
        
        if result.errors:
            print(f"数据获取错误: {result.errors}", file=sys.stderr)
        
        # 生成输出
        payload = tool.to_prompt_payload(result, mode=args.mode)
        
        if args.format == 'json':
            print(json.dumps(payload, ensure_ascii=False, indent=2))
        else:
            # 简要总结格式
            print(f"数据覆盖期: {payload['as_of']}")
            print(f"成功获取 {len(payload['stocks'])} 只股票数据")
            for stock in payload['stocks']:
                name = stock.get('stock_name', stock['code'])
                price = stock.get('last_close', 0)
                ret_3m = stock.get('cumret_3m', 0)
                print(f"- {name}: {price:.2f}元 (3月涨跌: {ret_3m*100:+.1f}%)")
        
        return 0
        
    except Exception as e:
        print(f"执行错误: {e}", file=sys.stderr)
        return 1

if __name__ == "__main__":
    sys.exit(main())
