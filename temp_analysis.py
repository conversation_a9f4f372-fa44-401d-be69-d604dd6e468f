#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时股票分析脚本
"""

from scripts.stock_data_tool import StockDataTool

def main():
    # 设置A股基准为沪深300
    tool = StockDataTool(benchmark='000300.SH')
    
    # 输入的股票代码
    user_text = '长飞光纤 601869.SH 中天科技 600522.SH 亨通光电 600487.SH'
    
    try:
        print("=== 开始分析 ===")
        
        # 解析输入
        codes, ocr_info = tool.enhanced_parse_inputs(codes_text=user_text, image_paths=[])
        print(f"解析的股票代码: {codes}")
        
        # 获取数据
        print("正在获取股票数据...")
        res = tool.fetch(codes)
        
        # 生成格式化报告
        print("生成分析报告...")
        formatted_report = tool.format_analysis_report(res, mode='quant')
        print("\n=== 格式化分析报告 ===")
        print(formatted_report)
        
        # 获取原始数据用于详细分析
        payload = tool.to_prompt_payload(res, mode='quant')
        
        print("\n=== 数据覆盖信息 ===")
        if isinstance(payload, dict):
            print(f"数据截止日期: {payload.get('as_of', 'Unknown')}")
            errors = payload.get('errors', [])
            if errors:
                print(f"数据错误: {errors}")
            else:
                print("数据获取成功，无错误")
                
            # 详细分析每只股票
            stocks = payload.get('stocks', [])
            for i, stock in enumerate(stocks):
                symbol = stock.get('symbol', 'Unknown')
                print(f"\n=== {symbol} 详细数据 ===")
                
                # 基本信息
                last_close = stock.get('last_close')
                cumret_3m = stock.get('cumret_3m')
                sigma_d = stock.get('sigma_d')
                
                print(f"最新收盘价: {last_close}")
                print(f"三个月累计收益率: {cumret_3m}")
                print(f"日波动率: {sigma_d}")
                
                # 技术指标
                rsi14 = stock.get('rsi14')
                macd = stock.get('macd')
                macd_hist = stock.get('macd_hist')
                
                print(f"RSI(14): {rsi14}")
                print(f"MACD: {macd}")
                print(f"MACD柱线: {macd_hist}")
                
                # 预测数据
                forecast = stock.get('forecast_20d', {})
                if forecast:
                    exp = forecast.get('exp')
                    range68 = forecast.get('range68')
                    range95 = forecast.get('range95')
                    
                    print(f"20日预期收益: {exp}")
                    print(f"68%置信区间: {range68}")
                    print(f"95%置信区间: {range95}")
        
    except Exception as e:
        print(f"执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
